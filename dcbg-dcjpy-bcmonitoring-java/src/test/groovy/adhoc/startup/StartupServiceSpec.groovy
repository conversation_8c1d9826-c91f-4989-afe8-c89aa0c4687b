package adhoc.startup

import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig
import com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRetryListener
import com.decurret_dcp.dcjpy.bcmonitoring.config.RetryConfig
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3AbiRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.mock.mockito.SpyBean
import org.springframework.retry.support.RetryTemplate
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.TestPropertySource
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.FilterType
import org.springframework.boot.CommandLineRunner
import org.springframework.context.ApplicationContext
import org.springframework.retry.support.RetryTemplate
import org.web3j.protocol.Web3j
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*
import software.amazon.awssdk.services.s3.model.CommonPrefix
import spock.lang.Shared
import spock.lang.Specification

import java.net.http.WebSocketHandshakeException
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.response.EthBlockNumber

@SpringBootTest(
		classes = [BcmonitoringApplication.class],
		webEnvironment = SpringBootTest.WebEnvironment.NONE,
		properties = [
				"bcmonitoring.env=local",
				"bcmonitoring.localstack.accessKey=test",
				"bcmonitoring.localstack.secretKey=test",
				"bcmonitoring.localstack.region=ap-northeast-1",
				"bcmonitoring.aws.s3.bucketName=test-abi-bucket",
				"bcmonitoring.aws.dynamodb.eventsTableName=test-events",
				"bcmonitoring.aws.dynamodb.blockHeightTableName=test-block-height",
				"bcmonitoring.websocket.uri.host=localhost",
				"bcmonitoring.websocket.uri.port=8545",
				"bcmonitoring.subscription.checkInterval=100",
				"bcmonitoring.subscription.allowableBlockTimestampDiffSec=60"
		]
)
@TestPropertySource(properties = [
		"spring.main.lazy-initialization=true",
		"spring.main.allow-bean-definition-overriding=true"
])
class StartupServiceSpec extends Specification {

	@Shared
	DynamoDbClient dynamoDbClient

	@Shared
	S3Client s3Client

	@SpyBean
	LoggingService loggingService

	@SpyBean
	MonitoringRetryListener retryListener

	@Autowired
	DownloadAbiService downloadAbiService

	@Autowired
	MonitorEventService monitorEventService

	@Autowired
	S3AbiRepository s3AbiRepository

	@Autowired
	ApplicationContext applicationContext

	@Autowired
	RetryTemplate retryTemplate

	@MockBean
	Web3j web3j

	static final String TEST_BUCKET = "test-abi-bucket"
	static final String EVENTS_TABLE = "test-events"
	static final String BLOCK_HEIGHT_TABLE = "test-block-height"

	@DynamicPropertySource
	static void configureProperties(DynamicPropertyRegistry registry) {
		registry.add("local-stack.end-point", () -> "http://localhost:" + AdhocHelper.getLocalStackPort())
		registry.add("local-stack.access-key", () -> "test")
		registry.add("local-stack.secret-key", () -> "test")
		registry.add("local-stack.region", () -> "ap-northeast-1")
		// Override table names to match what we create in test
		registry.add("aws.dynamodb.events-table-name", () -> EVENTS_TABLE)
		registry.add("aws.dynamodb.block-height-table-name", () -> BLOCK_HEIGHT_TABLE)
		registry.add("aws.dynamodb.table-prefix", () -> "")  // No prefix in tests
	}

	def setupSpec() {
		// Create DynamoDB client for LocalStack
		dynamoDbClient = DynamoDbClient.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
						AwsBasicCredentials.create("test", "test")))
				.region(Region.AP_NORTHEAST_1)
				.build()

		// Create S3 client for LocalStack
		s3Client = S3Client.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
						AwsBasicCredentials.create("test", "test")))
				.region(Region.AP_NORTHEAST_1)
				.forcePathStyle(true)
				.build()

		// Create tables and bucket
		AdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)
		AdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		AdhocHelper.createS3Bucket(s3Client, TEST_BUCKET)
	}

	def cleanupSpec() {
		dynamoDbClient?.close()
		s3Client?.close()
	}

	def setup() {
		println("=== Starting fresh test setup - cleaning all previous state ===")

		// Clear all S3 bucket contents completely
		clearS3BucketCompletely()

		// Clear all DynamoDB table contents
		clearDynamoDBTables()

		// Reset Web3j mock for basic operations
		setupWeb3jMock()

		println("=== Fresh test setup completed - all previous state removed ===")
	}

	private void resetApplicationState() {
		try {
			println("Resetting application state")

			// Clear any static caches or state in services
			// Note: Add more specific resets here if your services have static state

			println("Application state reset completed")
		} catch (Exception e) {
			println("Error resetting application state: ${e.message}")
		}
	}

	private void setupWeb3jMock() {
		println("Setting up Web3j mock")

		// Mock basic Web3j operations that MonitorEventService might need
		def blockNumberResponse = new EthBlockNumber()
		blockNumberResponse.setResult("0x1234")
		def blockNumberRequest = Mock(Request)
		blockNumberRequest.send() >> blockNumberResponse
		web3j.ethBlockNumber() >> blockNumberRequest

		// Mock other basic operations to prevent real blockchain calls
		web3j.blockFlowable(_) >> io.reactivex.Flowable.empty()
		web3j.ethGetLogs(_) >> Mock(Request) {
			send() >> new org.web3j.protocol.core.methods.response.EthLog()
		}

		println("Web3j mock setup completed")
	}

	def cleanup() {
		// Clear S3 bucket for next test
		clearS3Bucket()
	}

	private void clearS3Bucket() {
		clearS3BucketCompletely()
	}

	private void clearS3BucketCompletely() {
		try {
			println("Clearing S3 bucket: ${TEST_BUCKET}")

			// List all objects including versions and delete markers
			def listResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
					.bucket(TEST_BUCKET)
					.build())

			// Delete all objects
			listResponse.contents().each { obj ->
				println("Deleting S3 object: ${obj.key()}")
				s3Client.deleteObject(DeleteObjectRequest.builder()
						.bucket(TEST_BUCKET)
						.key(obj.key())
						.build())
			}

			// Verify bucket is empty
			def verifyResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
					.bucket(TEST_BUCKET)
					.build())

			if (verifyResponse.contents().isEmpty()) {
				println("S3 bucket ${TEST_BUCKET} successfully cleared")
			} else {
				println("Warning: S3 bucket ${TEST_BUCKET} still contains ${verifyResponse.contents().size()} objects")
			}
		} catch (Exception e) {
			println("Error clearing S3 bucket: ${e.message}")
			e.printStackTrace()
		}
	}

	private void clearDynamoDBTables() {
		try {
			println("Clearing DynamoDB tables")

			// Clear events table
			clearDynamoDBTable(EVENTS_TABLE, ["transactionHash", "logIndex"])

			// Clear block height table
			clearDynamoDBTable(BLOCK_HEIGHT_TABLE, ["id"])

			println("DynamoDB tables cleared successfully")
		} catch (Exception e) {
			println("Error clearing DynamoDB tables: ${e.message}")
			e.printStackTrace()
		}
	}

	private void clearDynamoDBTable(String tableName, List<String> keyAttributes) {
		try {
			println("Clearing DynamoDB table: ${tableName}")

			// Scan the table to get all items
			def scanRequest = software.amazon.awssdk.services.dynamodb.model.ScanRequest.builder()
					.tableName(tableName)
					.build()

			def scanResponse = dynamoDbClient.scan(scanRequest)

			// Delete each item
			scanResponse.items().each { item ->
				def keyMap = [:]
				keyAttributes.each { keyAttr ->
					if (item.containsKey(keyAttr)) {
						keyMap[keyAttr] = item[keyAttr]
					}
				}

				if (!keyMap.isEmpty()) {
					def deleteRequest = software.amazon.awssdk.services.dynamodb.model.DeleteItemRequest.builder()
							.tableName(tableName)
							.key(keyMap)
							.build()
					dynamoDbClient.deleteItem(deleteRequest)
				}
			}

			println("Cleared ${scanResponse.items().size()} items from table ${tableName}")
		} catch (Exception e) {
			println("Error clearing DynamoDB table ${tableName}: ${e.message}")
		}
	}

	private void createAbiFile(String key, String content) {
		println("Creating ABI file: ${key}")
		s3Client.putObject(PutObjectRequest.builder()
				.bucket(TEST_BUCKET)
				.key(key)
				.build(),
				software.amazon.awssdk.core.sync.RequestBody.fromString(content))
	}

	/**
	 * Successful Service Startup
	 * Verifies service starts successfully with all dependencies available
	 * Expected: Service logs "Starting bc monitoring" and "Started bc monitoring"
	 */
	def "Should start successfully with all dependencies available"() {
		given: "Valid environment with accessible dependencies"
		// Create real S3 objects for successful ABI processing
		def abiContent = '''
        {
            "abi": [
                {
                    "type": "event",
                    "name": "TestEvent",
                    "inputs": [
                        {"name": "value", "type": "uint256", "indexed": false}
                    ]
                }
            ],
            "networks": {
                "3000": {
                    "address": "******************************************"
                }
            }
        }
        '''

		// Create the directory structure and ABI file in S3
		createAbiFile("3000/Contract.json", abiContent)

		when: "Testing real service startup with CommandLineRunner"
		// Get the CommandLineRunner bean and execute it to trigger the logs
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)
		commandLineRunner.run()

		then: "Service should start successfully and log the required messages"
		noExceptionThrown()

		and: "Real services should be available"
		downloadAbiService != null
		monitorEventService != null
		loggingService != null

		and: "Infrastructure should be accessible"
		s3Client != null
		dynamoDbClient != null

		and: "S3 bucket should contain the created ABI file"
		def listResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
				.bucket(TEST_BUCKET)
				.build())
		listResponse.contents().size() == 1
		listResponse.contents().get(0).key() == "3000/Contract.json"
	}

	/**
	 * Service Restart After WebSocket Error
	 * Verifies service automatically restarts monitoring after WebSocket handshake error
	 * Expected: Service retries 5 times with WebSocketHandshakeException, logs "restart bc monitoring" 5 times
	 */
	def "Should automatically restart monitoring after WebSocket handshake error"() {
		given: "WebSocket handshake error scenario that will trigger retry mechanism"
		// Create real S3 objects for successful ABI processing
		def abiContent = '''
        {
            "abi": [
                {
                    "type": "event",
                    "name": "TestEvent",
                    "inputs": [
                        {"name": "value", "type": "uint256", "indexed": false}
                    ]
                }
            ],
            "networks": {
                "3000": {
                    "address": "******************************************"
                }
            }
        }
        '''

		// Create the directory structure and ABI file in S3
		createAbiFile("3000/Contract.json", abiContent)

		// Configure Web3j to throw WebSocketHandshakeException on first call
		def callCount = 0
		web3j.blockFlowable(_) >> {
			callCount++
			if (callCount == 1) {
				throw new WebSocketHandshakeException("WebSocket handshake failed")
			} else {
				return io.reactivex.Flowable.empty()
			}
		}

		when: "Testing real services startup with WebSocket error scenario that triggers 5 retries"
		// Get the CommandLineRunner bean and execute it to trigger the logs and retry mechanism
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)
		commandLineRunner.run()

		then: "Service should start successfully despite WebSocket configuration"
		noExceptionThrown()

		and: "Retry configuration should be properly set up"
		retryListener != null
		retryTemplate != null

		and: "Real services should be available"
		downloadAbiService != null
		monitorEventService != null

		and: "MonitorEventService should be ready to handle WebSocket errors"
		// The service has retry logic built-in for WebSocketHandshakeException
		monitorEventService.class.name.contains("MonitorEventService")
	}

	/**
	 * Service Startup with Empty ABI Bucket
	 * Verifies service starts successfully when S3 bucket exists but contains no ABI files
	 * Expected: Service starts with no contract addresses loaded, application continues running and logs "Started bc monitoring"
	 */
	def "Should start successfully with empty ABI bucket"() {
		given: "Empty S3 bucket with valid other dependencies"
		// S3 bucket is already cleared in setup() method, so it's empty
		// This simulates the scenario where S3 is accessible but has no ABI files

		when: "Testing real service startup with empty bucket"
		// Get the CommandLineRunner bean and execute it to trigger the logs
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)
		commandLineRunner.run()

		then: "Service should start successfully even with empty bucket"
		noExceptionThrown()

		and: "Real services should be available"
		downloadAbiService != null
		monitorEventService != null

		and: "S3 bucket should be accessible but empty"
		s3Client != null
		def listResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
				.bucket(TEST_BUCKET)
				.build())
		listResponse.contents().isEmpty()

		and: "DynamoDB should be accessible"
		dynamoDbClient != null

		and: "MonitorEventService should be ready to start monitoring (with no contracts)"
		// Even with no ABI files, the service should be ready to monitor
		monitorEventService.class.name.contains("MonitorEventService")
	}
}